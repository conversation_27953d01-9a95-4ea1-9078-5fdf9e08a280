# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
import re
from google import genai
from google.genai import types
import json


def clean_json_response(response_text: str) -> str:
    """
    Clean AI response text to make it valid JSON by removing control characters
    and fixing common formatting issues.

    Args:
        response_text: Raw response text from AI model

    Returns:
        Cleaned text ready for JSON parsing
    """
    # Remove any markdown code block formatting
    cleaned = response_text.strip()
    if cleaned.startswith('```json'):
        cleaned = cleaned[7:]
    if cleaned.startswith('```'):
        cleaned = cleaned[3:]
    if cleaned.endswith('```'):
        cleaned = cleaned[:-3]

    # Remove control characters except for allowed ones (newlines in strings are OK)
    # This regex removes control characters but preserves newlines within quoted strings
    cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)

    # Fix common JSON issues
    cleaned = cleaned.strip()

    return cleaned


def safe_json_parse(response_text: str) -> dict:
    """
    Safely parse JSON response with error handling and fallback.

    Args:
        response_text: Raw response text from AI model

    Returns:
        Parsed JSON as dictionary, or error structure if parsing fails
    """
    try:
        # First, clean the response
        cleaned_text = clean_json_response(response_text)

        # Try to parse the cleaned JSON
        return json.loads(cleaned_text)

    except json.JSONDecodeError as e:
        print(f"JSON parsing error in generate_script: {e}")
        print(f"Problematic text (first 500 chars): {response_text[:500]}")

        # Return a fallback structure
        return {
            "story": "Error generating story due to JSON parsing issue.",
            "error": f"JSON parsing failed: {str(e)}",
            "raw_response": response_text[:1000]  # Include first 1000 chars for debugging
        }
    except Exception as e:
        print(f"Unexpected error in JSON parsing: {e}")
        return {
            "story": "Error generating story due to unexpected issue.",
            "error": f"Unexpected error: {str(e)}"
        }

async  def generate(content):
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=content),
            ],
        )
    ]
    generate_content_config = types.GenerateContentConfig(
        response_mime_type="application/json",
        system_instruction=[
            types.Part.from_text(text="""You are a native Nepali storyteller with deep knowledge of Nepal’s geography, history, culture, and everyday life.

Your job is to generate a short, emotional, and realistic story **in the Nepali language** that can help learners understand Nepal’s heritage, values, and lifestyle.

Make it sound like you’re telling the story aloud to a friend or a child in a warm, heartfelt tone. Use **spoken Nepali**, not overly formal or literary language.

Do not explain the story. Do not translate. Just return the final story in Nepali that sounds natural when read aloud.

The story should be based on the following user-provided information:
                                 output format : { "story":" the story"}
"""),
        ],
    )

    response =  client.models.generate_content(
        model=model,
        contents=contents,
        config=generate_content_config,
    )



    output = json.loads(response.text)

    print("Generated Story:")
    print(output)
    print("type of the story:", type(output))
    story_script = output.get("story", None)
    if not story_script:
        raise Exception("Story generation failed")
    return story_script, response.usage_metadata
if __name__ == "__main__":
    import asyncio  
    content="""A 10-year-old boy named Arjun who lives near Phewa Lake in Pokhara. His grandfather tells him stories about Gurkha soldiers. He wants to be brave like them. He speaks Nepali and loves boating."""
    asyncio.run(generate(content))
